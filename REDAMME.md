Please ignore csv and parquet files as they are very large.


I am getting the below error when run my python script - i need it to process all files in the directory provided they do not have large holes in the date range.

python process_1.py
/home/<USER>/Documents/TICKS/.venv/lib/python3.13/site-packages/pandas_ta/__init__.py:7: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import get_distribution, DistributionNotFound
Starting STRATEGIST FEATURE DUAL-SIDE batch processing...
============================================================

--- Analyzing file: EURCHF_Ticks.csv ---
  - Auto-detected timestamp format: '%Y.%m.%d %H:%M:%S%.f'
  - Performing gap analysis...
  [Error] During gap check for EURCHF_Ticks.csv: 'Expr' object has no attribute 'cumsum'
  [SKIP] Could not read date range or file is empty after parsing.

--- Analyzing file: EURUSD_Ticks.csv ---
Output file 'EURUSD_Features.parquet' already exists. Overwrite? (y/n): n
  [SKIP] User chose not to overwrite existing file.

--- Analyzing file: GBPCHF_Ticks.csv ---
  - Auto-detected timestamp format: '%Y.%m.%d %H:%M:%S%.f'
  - Performing gap analysis...
  [Error] During gap check for GBPCHF_Ticks.csv: 'Expr' object has no attribute 'cumsum'
  [SKIP] Could not read date range or file is empty after parsing.

--- Analyzing file: GBPJPY_Ticks.csv ---
  - Auto-detected timestamp format: '%Y.%m.%d %H:%M:%S%.f'
  - Performing gap analysis...
  [Error] During gap check for GBPJPY_Ticks.csv: 'Expr' object has no attribute 'cumsum'
  [SKIP] Could not read date range or file is empty after parsing.

--- Analyzing file: GBPUSD_Ticks.csv ---
  - Auto-detected timestamp format: '%Y.%m.%d %H:%M:%S%.f'
  - Performing gap analysis...
  [Error] During gap check for GBPUSD_Ticks.csv: 'Expr' object has no attribute 'cumsum'
  [SKIP] Could not read date range or file is empty after parsing.

--- Analyzing file: USDCHF_Ticks.csv ---
  - Auto-detected timestamp format: '%Y.%m.%d %H:%M:%S%.f'
  - Performing gap analysis...
  [Error] During gap check for USDCHF_Ticks.csv: 'Expr' object has no attribute 'cumsum'
  [SKIP] Could not read date range or file is empty after parsing.

--- Analyzing file: USDJPY_Ticks.csv ---
  - Auto-detected timestamp format: '%Y.%m.%d %H:%M:%S%.f'
  - Performing gap analysis...
  [Error] During gap check for USDJPY_Ticks.csv: 'Expr' object has no attribute 'cumsum'
  [SKIP] Could not read date range or file is empty after parsing.