"""
Feature-Rich Parquet File Verification Script

Description:
This script audits the Parquet files generated by the main processing script
('process_1.py'). It is designed to be run after the main script completes to
ensure the output data is clean, complete, and ready for use in a Machine
Learning pipeline.

Key Checks Performed:
- File Integrity: Checks if the Parquet file can be read without errors.
- Data Shape: Reports the number of rows and columns.
- Time Range: Verifies the start and end timestamps of the data.
- Null Values: Performs a critical check to confirm that NO null values exist in the
  final dataset, as they should have been dropped by the processing script.
- Data Types: Summarizes the data types of all columns to ensure they are as expected.
- Naming Convention: Displays sample column names for a quick visual check.

How to Run:
1. Place this script in the same directory as your generated '*_Features.parquet' files.
2. Ensure you are in the same Conda environment used to run the main script.
   (conda activate my_forex_env)
3. Execute the script from your terminal:
   python verify_features.py
"""
import polars as pl
import glob
from collections import defaultdict

def verify_feature_files():
    """
    Finds and verifies all feature Parquet files in the current directory.
    """
    # Find all files matching the output pattern of the main script
    feature_files = sorted(glob.glob('*_Features.parquet'))

    if not feature_files:
        print("Verification failed: No '*_Features.parquet' files found in this directory.")
        print("Please run the main processing script first.")
        return

    print(f"Found {len(feature_files)} feature files to verify.")
    print("=" * 60)

    all_passed = True
    for file_path in feature_files:
        print(f"--- Verifying: {file_path} ---")
        try:
            # Step 1: Read the Parquet file
            df = pl.read_parquet(file_path)

            # Step 2: Gather statistics
            rows, cols = df.shape
            mem_mb = df.estimated_size('mb')
            start_time = df.select(pl.min("Time (UTC)")).item()
            end_time = df.select(pl.max("Time (UTC)")).item()

            # Step 3: Perform the critical null check
            # df.null_count() returns a DataFrame of null counts per column.
            # The two .sum() calls aggregate this to a single number.
            total_nulls = df.null_count().sum().sum().item()

            # Step 4: Summarize data types
            dtype_counts = defaultdict(int)
            for dtype in df.dtypes:
                dtype_counts[str(dtype)] += 1

            # Step 5: Print the report for this file
            print(f"  - Shape          : {rows:,} rows, {cols:,} columns")
            print(f"  - Time Range     : {start_time} to {end_time}")
            print(f"  - Memory Size    : {mem_mb:.2f} MB")

            # Print the null check result with a clear pass/fail status
            if total_nulls == 0:
                print(f"  - Null Check     : {total_nulls} nulls found. [PASSED]")
            else:
                print(f"  - Null Check     : {total_nulls} nulls found! [FAILED]")
                all_passed = False
            
            print("  - Data Types Summary:")
            for dtype, count in dtype_counts.items():
                print(f"    - {dtype:<15}: {count} columns")

            print("  - Column Name Sanity Check:")
            print(f"    - First Column   : {df.columns[0]}")
            print(f"    - First Feature  : {df.columns[1]}")
            print(f"    - Last Feature   : {df.columns[-1]}")
            print("-" * 60)

        except Exception as e:
            print(f"[ERROR] Could not process file {file_path}. It may be corrupted.")
            print(f"  - Details: {e}")
            print("-" * 60)
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("Verification Complete: All files passed inspection and appear ready for ML.")
    else:
        print("Verification Complete: One or more files FAILED inspection. Please review the logs above.")
    print("=" * 60)


if __name__ == "__main__":
    verify_feature_files()
