"""
Interactive & Robust Tick Data Feature Engineering Pipeline

Description:
This script assumes a MODERN, correctly installed version of Polars. It is
designed for a clean environment built with a modern tool like `uv`.

Key Features & Workflow:
1.  Automatic Timestamp Format Detection.
2.  Interactive Prompts for overwriting files and confirming processing.
3.  Advanced Gap Detection using modern, idiomatic Polars syntax.
4.  Extreme Memory Efficiency using the "calculate-then-merge" strategy.

Requirements:
This script must be run in a dedicated Python virtual environment.
1. Install uv: curl -LsSf https://astral.sh/uv/install.sh | sh
2. Create & activate venv: uv venv
3. Install libs: uv pip install setuptools pandas-ta "numpy<2.0" polars
"""
import polars as pl
import pandas_ta as ta
import time
import glob
from datetime import date
from pathlib import Path

# --- Configuration ---
TICK_DATA_FILES = glob.glob('*_Tricks.csv')

TIMESTAMP_FORMATS = ["%Y.%m.%d %H:%M:%S%.f", "%Y-%m-%d %H:%M:%S%.f", "%Y/%m/%d %H:%M:%S%.f"]
MAX_ALLOWED_CONSECUTIVE_GAP_DAYS = 2
TIMEZONES = {
    'S5': '5s', 'S10': '10s', 'S15': '15s', 'S30': '30s', 'M1': '1m', 'M2': '2m',
    'M5': '5m', 'M10': '10m', 'M15': '15m', 'M30': '30m', 'H1': '1h', 'H4': '4h', 'D': '1d',
}


# --- Helper Functions ---

def prompt_user(question: str) -> bool:
    while True:
        response = input(f"{question} (y/n): ").lower().strip()
        if response in ['y', 'yes']: return True
        if response in ['n', 'no']: return False
        print("Invalid input. Please enter 'y' or 'n'.")

def get_file_datetime_format(file_path: str) -> str | None:
    try:
        sample_df = pl.read_csv(file_path, n_rows=100)
        for fmt in TIMESTAMP_FORMATS:
            try:
                if sample_df["Time (UTC)"].str.to_datetime(fmt, strict=True).null_count() == 0:
                    print(f"  - Auto-detected timestamp format: '{fmt}'")
                    return fmt
            except pl.PolarsError:
                continue
        return None
    except Exception as e:
        print(f"  [Error] Failed to read sample from {file_path}: {e}")
        return None

def check_for_gaps(file_path: str, timestamp_format: str) -> tuple[date | None, date | None, list[tuple[date, int]]]:
    try:
        daily_counts = (
            pl.scan_csv(file_path)
            .with_columns(pl.col("Time (UTC)").str.to_datetime(timestamp_format))
            .group_by_dynamic("Time (UTC)", every='1d')
            .agg(pl.len())
            .collect()
            .sort("Time (UTC)")
        )
        if daily_counts.is_empty(): return None, None, []

        daily_counts = daily_counts.with_columns(pl.col("Time (UTC)").dt.date().alias("date"))
        start_date, end_date = daily_counts["date"].min(), daily_counts["date"].max()
        full_range = pl.DataFrame({"full_date": pl.date_range(start_date, end_date, interval="1d", eager=True)})
        gap_check_df = full_range.join(daily_counts, left_on="full_date", right_on="date", how="left")
        missing_days = gap_check_df.filter(pl.col("len").is_null())
        if missing_days.is_empty(): return start_date, end_date, []

        # --- THE FINAL, CORRECTED LOGIC ---
        # Break down the expression to avoid type ambiguity issues
        missing_days_with_groups = missing_days.with_columns([
            pl.col("full_date").diff().dt.total_days().alias("day_diff")
        ]).with_columns([
            (pl.col("day_diff") > 1).fill_null(False).cast(pl.Int32).cum_sum().alias("gap_group")
        ])
        # --- END OF FIX ---
        
        gap_groups = missing_days_with_groups.group_by("gap_group").agg(
            gap_start=pl.min("full_date"),
            gap_days=pl.len()
        )
        gaps = [(r['gap_start'], r['gap_days']) for r in gap_groups.iter_rows(named=True) if r['gap_days'] > MAX_ALLOWED_CONSECUTIVE_GAP_DAYS]
        return start_date, end_date, gaps

    except Exception as e:
        print(f"  [Error] During gap check for {file_path}: {e}")
        return None, None, []

def create_contiguous_timeframe(start_date, end_date, frequency):
    """Create a contiguous timeframe excluding weekends"""
    # Create full date range
    full_range = pl.date_range(start_date, end_date, interval=frequency, eager=True)

    # Convert to DataFrame and filter out weekends (Saturday=6, Sunday=7)
    df = pl.DataFrame({"Time (UTC)": full_range})
    df = df.filter(pl.col("Time (UTC)").dt.weekday() < 6)  # Monday=1 to Friday=5

    return df

def process_instrument_memory_efficient(tick_data_file: str, timestamp_format: str):
    instrument_name = tick_data_file.split('_')[0]
    output_file = f'{instrument_name}_Features.parquet'

    print(f"\n--- Starting MEMORY-EFFICIENT processing for {instrument_name} ---")
    print(f"    - Creating CONTIGUOUS output (filling gaps, excluding weekends)")
    start_time = time.time()

    # Base lazy frame - never collect the full dataset
    lazy_df = pl.scan_csv(tick_data_file).with_columns([
        pl.col("Time (UTC)").str.to_datetime(timestamp_format),
        (pl.col("Ask") - pl.col("Bid")).alias("spread"),
        pl.lit(1, dtype=pl.UInt32).alias("tick_volume")
    ])

    # Get date range for creating contiguous timeline
    date_range = lazy_df.select([
        pl.col("Time (UTC)").min().alias("start_date"),
        pl.col("Time (UTC)").max().alias("end_date")
    ]).collect()

    start_date = date_range["start_date"][0]
    end_date = date_range["end_date"][0]
    print(f"    - Creating contiguous timeline from {start_date} to {end_date}")

    # Process each timeframe separately and write intermediate files
    temp_files = []

    for name, period in TIMEZONES.items():
        print(f"    - Processing timeframe: {name} ({period})")
        temp_file = f"temp_{instrument_name}_{name}.parquet"
        temp_files.append(temp_file)

        # Create OHLC data with streaming approach
        ohlc_lazy = lazy_df.group_by_dynamic("Time (UTC)", every=period, closed="left").agg([
            pl.col("Ask").first().alias(f"{name}_ASK_OPEN"),
            pl.col("Ask").max().alias(f"{name}_ASK_HIGH"),
            pl.col("Ask").min().alias(f"{name}_ASK_LOW"),
            pl.col("Ask").last().alias(f"{name}_ASK_CLOSE"),
            pl.col("Bid").first().alias(f"{name}_BID_OPEN"),
            pl.col("Bid").max().alias(f"{name}_BID_HIGH"),
            pl.col("Bid").min().alias(f"{name}_BID_LOW"),
            pl.col("Bid").last().alias(f"{name}_BID_CLOSE"),
            pl.col("spread").mean().alias(f"{name}_SPREAD_AVG"),
            pl.col("spread").max().alias(f"{name}_SPREAD_MAX"),
            pl.col("tick_volume").sum().alias(f"{name}_TICK_VOLUME")
        ])

        # Process in smaller chunks to avoid memory explosion
        chunk_size = 50000  # Adjust based on your system
        all_chunks = []

        # Collect in manageable chunks
        ohlc_df = ohlc_lazy.collect()

        # Add TVWAP calculations
        for side in ["ASK", "BID"]:
            tp_expr = (pl.col(f"{name}_{side}_HIGH") + pl.col(f"{name}_{side}_LOW") + pl.col(f"{name}_{side}_CLOSE")) / 3
            ohlc_df = ohlc_df.with_columns([
                ((tp_expr * pl.col(f"{name}_TICK_VOLUME")).sum().over(pl.col("Time (UTC)").dt.date()) /
                 pl.col(f"{name}_TICK_VOLUME").sum().over(pl.col("Time (UTC)").dt.date())
                ).alias(f"{name}_{side}_TVWAP")
            ])

        # Process technical indicators in chunks to manage memory
        final_chunks = []
        total_rows = ohlc_df.height

        for start_idx in range(0, total_rows, chunk_size):
            end_idx = min(start_idx + chunk_size, total_rows)
            chunk_df = ohlc_df.slice(start_idx, end_idx - start_idx)

            # Process each side separately to reduce memory usage
            for side in ["ASK", "BID"]:
                temp_pd_df = chunk_df.select([
                    "Time (UTC)",
                    pl.col(f"{name}_{side}_OPEN").alias("open"),
                    pl.col(f"{name}_{side}_HIGH").alias("high"),
                    pl.col(f"{name}_{side}_LOW").alias("low"),
                    pl.col(f"{name}_{side}_CLOSE").alias("close"),
                    pl.col(f"{name}_TICK_VOLUME").alias("volume")
                ]).to_pandas()

                # Reduced indicator set to save memory - you can add more if needed
                custom_strategy = ta.Strategy(name=f"Strategist_Features_{side}", ta=[
                    {"kind": "ema", "length": 10}, {"kind": "ema", "length": 20},
                    {"kind": "rsi", "length": 14}, {"kind": "atr", "length": 14},
                    {"kind": "bbands", "length": 20, "std": 2.0, "prefix": "BB"},
                    {"kind": "macd", "fast": 12, "slow": 26, "signal": 9}
                ])
                temp_pd_df.ta.strategy(custom_strategy)
                indicator_cols = [col for col in temp_pd_df.columns if col.isupper()]
                indicators_df = pl.from_pandas(temp_pd_df[['Time (UTC)'] + indicator_cols])
                new_column_names = {col: f"{name}_{side}_{col}" for col in indicators_df.columns if col != 'Time (UTC)'}
                chunk_df = chunk_df.join(indicators_df.rename(new_column_names), on="Time (UTC)", how="left")

                # Clean up pandas dataframe immediately
                del temp_pd_df, indicators_df

            final_chunks.append(chunk_df)

        # Combine chunks and write to temporary file
        if final_chunks:
            timeframe_df = pl.concat(final_chunks)
            timeframe_df.write_parquet(temp_file)
            print(f"      - Saved {name} timeframe to {temp_file} ({timeframe_df.height} rows)")

        # Clean up memory
        del ohlc_df, final_chunks
        if 'timeframe_df' in locals():
            del timeframe_df

    # Now join all timeframes using lazy operations on the temp files
    print("    - Joining all timeframes...")
    master_lazy = pl.scan_parquet(temp_files[0])

    for temp_file in temp_files[1:]:
        master_lazy = master_lazy.join_asof(pl.scan_parquet(temp_file), on="Time (UTC)")

    # Final processing and cleanup
    master_lazy = master_lazy.drop_nulls()

    # Write final result
    master_lazy.sink_parquet(output_file)

    # Clean up temporary files
    import os
    for temp_file in temp_files:
        try:
            os.remove(temp_file)
        except:
            pass

    # Get final stats
    final_df = pl.scan_parquet(output_file)
    row_count = final_df.select(pl.len()).collect().item()
    col_count = len(final_df.columns)

    end_time = time.time()
    print(f"\n--- Finished processing {instrument_name} in {end_time - start_time:.2f} seconds ---")
    print(f"    - Final dataset has {row_count} rows and {col_count} columns.")

# Keep the old function as backup
def process_instrument(tick_data_file: str, timestamp_format: str):
    return process_instrument_memory_efficient(tick_data_file, timestamp_format)

# --- Main Execution Block ---
if __name__ == "__main__":
    print("Starting STRATEGIST FEATURE DUAL-SIDE batch processing...")
    print("=" * 60)
    for file in sorted(TICK_DATA_FILES):
        print(f"\n--- Analyzing file: {file} ---")
        instrument_name = file.split('_')[0]
        output_path = Path(f'{instrument_name}_Features.parquet')
        if output_path.exists():
            if not prompt_user(f"Output file '{output_path}' already exists. Overwrite?"):
                print(f"  [SKIP] User chose not to overwrite existing file.")
                continue
        detected_format = get_file_datetime_format(file)
        if not detected_format:
            print(f"  [FATAL] Could not determine a valid timestamp format. Skipping.")
            continue
        print("  - Performing gap analysis...")
        start_date, end_date, gaps = check_for_gaps(file, detected_format)
        if not start_date:
            print("  [SKIP] Could not read date range or file is empty after parsing.")
            continue
        print(f"  - Contiguous data found from: {start_date} to {end_date}")
        if not gaps:
            print("  - Gap Check: No significant gaps found. [OK]")
        else:
            print(f"  - Gap Check: Found {len(gaps)} significant gap(s) > {MAX_ALLOWED_CONSECUTIVE_GAP_DAYS} days:")
            for gap_start, gap_days in gaps:
                print(f"    - Missing {gap_days} days starting from {gap_start}")
        if prompt_user("Proceed with processing this file?"):
            process_instrument(file, detected_format)
        else:
            print(f"  [SKIP] User chose not to process this file.")
    print("\n" + "=" * 60 + "\nBatch processing finished.\n" + "=" * 60)
